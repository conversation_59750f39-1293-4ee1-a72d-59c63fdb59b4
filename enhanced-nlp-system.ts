// Enhanced NLP Integration System for Chitu00
// Combines all advanced NLP components for comprehensive language understanding

import { AdvancedSentimentAnalyzer, SentimentResult } from './advanced-sentiment-analyzer';
import { IntentRecognitionSystem, IntentResult } from './intent-recognition-system';
import { ConversationContextManager, ConversationContext, ContextualResponse } from './conversation-context-manager';
import { AdvancedTopicModeling, TopicResult } from './advanced-topic-modeling';
import { CodeSwitchingDetector, CodeSwitchingResult } from './code-switching-detector';
import { MultilingualProcessor, MultilingualAnalysis } from './multilingual-processor';

export interface ComprehensiveNLPResult {
  // Core analysis
  language: MultilingualAnalysis;
  sentiment: SentimentResult;
  intent: IntentResult;
  topics: TopicResult;
  codeSwitching: CodeSwitchingResult;
  
  // Context and conversation
  conversationContext?: ConversationContext;
  contextualResponse?: ContextualResponse;
  
  // Meta information
  processingTime: number;
  confidence: number;
  complexity: number;
  recommendations: string[];
}

export interface NLPConfiguration {
  enableSentimentAnalysis: boolean;
  enableIntentRecognition: boolean;
  enableTopicModeling: boolean;
  enableCodeSwitchingDetection: boolean;
  enableContextManagement: boolean;
  defaultLanguage: string;
  confidenceThreshold: number;
}

export interface ProcessingMetrics {
  totalProcessed: number;
  averageProcessingTime: number;
  languageDistribution: { [language: string]: number };
  accuracyMetrics: { [component: string]: number };
  errorRate: number;
}

export class EnhancedNLPSystem {
  private sentimentAnalyzer: AdvancedSentimentAnalyzer;
  private intentRecognizer: IntentRecognitionSystem;
  private contextManager: ConversationContextManager;
  private topicModeler: AdvancedTopicModeling;
  private codeSwitchingDetector: CodeSwitchingDetector;
  private multilingualProcessor: MultilingualProcessor;
  
  private configuration: NLPConfiguration;
  private metrics: ProcessingMetrics;
  private cache: Map<string, ComprehensiveNLPResult>;

  constructor(config?: Partial<NLPConfiguration>) {
    this.sentimentAnalyzer = new AdvancedSentimentAnalyzer();
    this.intentRecognizer = new IntentRecognitionSystem();
    this.contextManager = new ConversationContextManager();
    this.topicModeler = new AdvancedTopicModeling();
    this.codeSwitchingDetector = new CodeSwitchingDetector();
    this.multilingualProcessor = new MultilingualProcessor();
    
    this.configuration = {
      enableSentimentAnalysis: true,
      enableIntentRecognition: true,
      enableTopicModeling: true,
      enableCodeSwitchingDetection: true,
      enableContextManagement: true,
      defaultLanguage: 'en',
      confidenceThreshold: 0.6,
      ...config
    };
    
    this.metrics = {
      totalProcessed: 0,
      averageProcessingTime: 0,
      languageDistribution: {},
      accuracyMetrics: {},
      errorRate: 0
    };
    
    this.cache = new Map();
  }

  // Main comprehensive analysis function
  async analyzeText(
    text: string, 
    sessionId?: string, 
    options?: { useCache?: boolean; language?: string }
  ): Promise<ComprehensiveNLPResult> {
    const startTime = Date.now();
    
    try {
      // Check cache first
      const cacheKey = this.generateCacheKey(text, sessionId);
      if (options?.useCache && this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey)!;
      }

      // Step 1: Basic multilingual processing
      const languageAnalysis = this.multilingualProcessor.analyzeText(text);
      const detectedLanguage = options?.language || languageAnalysis.detectedLanguage.language;

      // Step 2: Parallel processing of NLP components
      const analysisPromises: Promise<any>[] = [];
      
      if (this.configuration.enableSentimentAnalysis) {
        analysisPromises.push(
          Promise.resolve(this.sentimentAnalyzer.analyzeSentiment(text, detectedLanguage))
        );
      }

      if (this.configuration.enableIntentRecognition) {
        analysisPromises.push(
          Promise.resolve(this.intentRecognizer.recognizeIntent(text, detectedLanguage))
        );
      }

      if (this.configuration.enableTopicModeling) {
        analysisPromises.push(
          Promise.resolve(this.topicModeler.extractTopics(text, detectedLanguage))
        );
      }

      if (this.configuration.enableCodeSwitchingDetection) {
        analysisPromises.push(
          Promise.resolve(this.codeSwitchingDetector.detectCodeSwitching(text))
        );
      }

      // Wait for all analyses to complete
      const results = await Promise.all(analysisPromises);
      
      let sentimentResult: SentimentResult | undefined;
      let intentResult: IntentResult | undefined;
      let topicResult: TopicResult | undefined;
      let codeSwitchingResult: CodeSwitchingResult | undefined;
      
      let resultIndex = 0;
      if (this.configuration.enableSentimentAnalysis) {
        sentimentResult = results[resultIndex++];
      }
      if (this.configuration.enableIntentRecognition) {
        intentResult = results[resultIndex++];
      }
      if (this.configuration.enableTopicModeling) {
        topicResult = results[resultIndex++];
      }
      if (this.configuration.enableCodeSwitchingDetection) {
        codeSwitchingResult = results[resultIndex++];
      }

      // Step 3: Context management
      let conversationContext: ConversationContext | undefined;
      let contextualResponse: ContextualResponse | undefined;
      
      if (this.configuration.enableContextManagement && sessionId) {
        conversationContext = this.contextManager.getContext(sessionId);
        if (!conversationContext) {
          sessionId = this.contextManager.createContext();
          conversationContext = this.contextManager.getContext(sessionId);
        }
        
        // Add turn to conversation
        if (intentResult && sentimentResult) {
          this.contextManager.addTurn(sessionId, {
            timestamp: new Date(),
            userMessage: text,
            aiResponse: '', // Will be filled by response generator
            language: detectedLanguage,
            intent: intentResult.intent,
            sentiment: sentimentResult.sentiment,
            topics: topicResult?.topics.map(t => t.name) || [],
            entities: intentResult.entities,
            confidence: intentResult.confidence
          });
        }
        
        // Get contextual response
        if (intentResult) {
          contextualResponse = this.contextManager.getContextualResponse(
            sessionId, 
            text, 
            intentResult.intent
          );
        }
      }

      // Step 4: Calculate overall metrics
      const processingTime = Date.now() - startTime;
      const confidence = this.calculateOverallConfidence(
        languageAnalysis,
        sentimentResult,
        intentResult,
        topicResult,
        codeSwitchingResult
      );
      const complexity = this.calculateTextComplexity(text, languageAnalysis);
      const recommendations = this.generateRecommendations(
        languageAnalysis,
        sentimentResult,
        intentResult,
        topicResult,
        codeSwitchingResult
      );

      // Step 5: Compile comprehensive result
      const comprehensiveResult: ComprehensiveNLPResult = {
        language: languageAnalysis,
        sentiment: sentimentResult || this.getDefaultSentiment(),
        intent: intentResult || this.getDefaultIntent(),
        topics: topicResult || this.getDefaultTopics(),
        codeSwitching: codeSwitchingResult || this.getDefaultCodeSwitching(),
        conversationContext,
        contextualResponse,
        processingTime,
        confidence,
        complexity,
        recommendations
      };

      // Update metrics
      this.updateMetrics(comprehensiveResult);
      
      // Cache result
      if (options?.useCache) {
        this.cache.set(cacheKey, comprehensiveResult);
      }

      return comprehensiveResult;
      
    } catch (error) {
      console.error('NLP Analysis Error:', error);
      this.metrics.errorRate++;
      return this.getErrorResult(text, Date.now() - startTime);
    }
  }

  // Batch processing for multiple texts
  async analyzeMultipleTexts(
    texts: string[], 
    sessionId?: string
  ): Promise<ComprehensiveNLPResult[]> {
    const results: ComprehensiveNLPResult[] = [];
    
    for (const text of texts) {
      const result = await this.analyzeText(text, sessionId, { useCache: true });
      results.push(result);
    }
    
    return results;
  }

  // Get conversation summary
  getConversationSummary(sessionId: string): string {
    return this.contextManager.getConversationSummary(sessionId);
  }

  // Get processing metrics
  getMetrics(): ProcessingMetrics {
    return { ...this.metrics };
  }

  // Update configuration
  updateConfiguration(config: Partial<NLPConfiguration>): void {
    this.configuration = { ...this.configuration, ...config };
  }

  private calculateOverallConfidence(
    language: MultilingualAnalysis,
    sentiment?: SentimentResult,
    intent?: IntentResult,
    topics?: TopicResult,
    codeSwitching?: CodeSwitchingResult
  ): number {
    const confidences: number[] = [language.detectedLanguage.confidence];
    
    if (sentiment) confidences.push(sentiment.confidence);
    if (intent) confidences.push(intent.confidence);
    if (topics) confidences.push(topics.confidence);
    if (codeSwitching) confidences.push(codeSwitching.confidence);
    
    return confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
  }

  private calculateTextComplexity(text: string, analysis: MultilingualAnalysis): number {
    const wordCount = text.split(/\s+/).length;
    const sentenceCount = text.split(/[.!?]+/).length;
    const avgWordsPerSentence = wordCount / Math.max(1, sentenceCount);
    
    // Complexity factors
    const lengthComplexity = Math.min(1, wordCount / 100);
    const structureComplexity = Math.min(1, avgWordsPerSentence / 20);
    const languageComplexity = analysis.detectedLanguage.script !== 'Latin' ? 0.2 : 0;
    
    return (lengthComplexity + structureComplexity + languageComplexity) / 3;
  }

  private generateRecommendations(
    language: MultilingualAnalysis,
    sentiment?: SentimentResult,
    intent?: IntentResult,
    topics?: TopicResult,
    codeSwitching?: CodeSwitchingResult
  ): string[] {
    const recommendations: string[] = [];
    
    // Language-based recommendations
    if (language.detectedLanguage.confidence < 0.7) {
      recommendations.push('Consider clarifying the language or providing more context');
    }
    
    // Sentiment-based recommendations
    if (sentiment && sentiment.confidence < 0.6) {
      recommendations.push('Sentiment analysis uncertain - consider more explicit emotional indicators');
    }
    
    // Intent-based recommendations
    if (intent && intent.confidence < 0.6) {
      recommendations.push('Intent unclear - consider rephrasing the request more directly');
    }
    
    // Code-switching recommendations
    if (codeSwitching && codeSwitching.isCodeSwitching) {
      recommendations.push('Code-switching detected - ensure consistent language for better understanding');
    }
    
    // Topic recommendations
    if (topics && topics.topics.length === 0) {
      recommendations.push('No clear topics identified - consider providing more specific content');
    }
    
    return recommendations;
  }

  private updateMetrics(result: ComprehensiveNLPResult): void {
    this.metrics.totalProcessed++;
    
    // Update average processing time
    this.metrics.averageProcessingTime = 
      (this.metrics.averageProcessingTime * (this.metrics.totalProcessed - 1) + result.processingTime) / 
      this.metrics.totalProcessed;
    
    // Update language distribution
    const language = result.language.detectedLanguage.language;
    this.metrics.languageDistribution[language] = 
      (this.metrics.languageDistribution[language] || 0) + 1;
    
    // Update accuracy metrics
    this.metrics.accuracyMetrics['overall'] = 
      (this.metrics.accuracyMetrics['overall'] || 0) + result.confidence;
  }

  private generateCacheKey(text: string, sessionId?: string): string {
    const textHash = text.substring(0, 50); // Simple hash
    return `${textHash}_${sessionId || 'no_session'}`;
  }

  private getDefaultSentiment(): SentimentResult {
    return {
      sentiment: 'neutral',
      score: 0,
      confidence: 0.5,
      emotions: [],
      intensity: 0,
      subjectivity: 0.5
    };
  }

  private getDefaultIntent(): IntentResult {
    return {
      intent: 'general_conversation',
      confidence: 0.5,
      entities: [],
      context: {
        domain: 'general',
        urgency: 0.5,
        formality: 0.5,
        emotionalState: 'neutral',
        requiresResponse: true
      },
      subIntents: []
    };
  }

  private getDefaultTopics(): TopicResult {
    return {
      topics: [],
      confidence: 0.3,
      language: 'en',
      coherence: 0.5
    };
  }

  private getDefaultCodeSwitching(): CodeSwitchingResult {
    return {
      isCodeSwitching: false,
      languages: [],
      dominantLanguage: 'en',
      switchingPoints: [],
      confidence: 0.5,
      switchingType: 'none'
    };
  }

  private getErrorResult(text: string, processingTime: number): ComprehensiveNLPResult {
    return {
      language: {
        detectedLanguage: { language: 'en', confidence: 0, script: 'Latin', direction: 'ltr', isRomanized: false },
        originalText: text,
        normalizedText: text,
        sentiment: 'neutral',
        emotion: 'neutral',
        topics: [],
        culturalMarkers: [],
        formalityLevel: 0.5
      },
      sentiment: this.getDefaultSentiment(),
      intent: this.getDefaultIntent(),
      topics: this.getDefaultTopics(),
      codeSwitching: this.getDefaultCodeSwitching(),
      processingTime,
      confidence: 0,
      complexity: 0.5,
      recommendations: ['Error occurred during processing - please try again']
    };
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
  }

  // Get cache size
  getCacheSize(): number {
    return this.cache.size;
  }
}
