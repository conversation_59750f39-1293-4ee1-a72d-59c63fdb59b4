// Advanced Topic Modeling System for Chitu00
// Supports English, Urdu, and Punjabi topic extraction and clustering

import stopwords from 'stopwords-iso';
import natural from 'natural';

export interface TopicResult {
  topics: Topic[];
  confidence: number;
  language: string;
  coherence: number;
}

export interface Topic {
  name: string;
  keywords: string[];
  weight: number;
  confidence: number;
  category: string;
  relatedTopics: string[];
}

export interface TopicCluster {
  clusterId: string;
  topics: Topic[];
  centroid: string[];
  coherence: number;
  size: number;
}

export interface DocumentTopics {
  documentId: string;
  topics: Topic[];
  primaryTopic: Topic;
  topicDistribution: { [topicName: string]: number };
}

export class AdvancedTopicModeling {
  private stopWords: Map<string, Set<string>>;
  private topicCategories: Map<string, string[]>;
  private stemmer: any;
  private tfidf: natural.TfIdf;

  constructor() {
    this.stopWords = new Map();
    this.topicCategories = new Map();
    this.stemmer = natural.PorterStemmer;
    this.tfidf = new natural.TfIdf();
    this.initializeStopWords();
    this.initializeTopicCategories();
  }

  private initializeStopWords(): void {
    // English stopwords
    this.stopWords.set('en', new Set([
      ...stopwords.en,
      'like', 'just', 'really', 'actually', 'basically', 'literally',
      'thing', 'stuff', 'things', 'something', 'anything', 'everything'
    ]));

    // Urdu stopwords
    this.stopWords.set('ur', new Set([
      'اور', 'کا', 'کی', 'کے', 'میں', 'سے', 'کو', 'نے', 'ہے', 'ہیں', 'تھا', 'تھی', 'تھے',
      'ہو', 'ہوں', 'ہوا', 'ہوئی', 'ہوئے', 'گا', 'گی', 'گے', 'یہ', 'وہ', 'اس', 'ان', 'کہ',
      'جو', 'جس', 'جن', 'کیا', 'کیوں', 'کیسے', 'کب', 'کہاں', 'کون', 'کچھ', 'کوئی', 'سب',
      'بھی', 'تو', 'ہی', 'نہیں', 'نہ', 'پر', 'لیے', 'ساتھ', 'بعد', 'پہلے', 'اب', 'پھر'
    ]));

    // Punjabi stopwords
    this.stopWords.set('pa', new Set([
      'ਅਤੇ', 'ਦਾ', 'ਦੀ', 'ਦੇ', 'ਵਿੱਚ', 'ਤੋਂ', 'ਨੂੰ', 'ਨੇ', 'ਹੈ', 'ਹਨ', 'ਸੀ', 'ਸਨ',
      'ਹੋ', 'ਹੋਵੇ', 'ਹੋਇਆ', 'ਹੋਈ', 'ਹੋਏ', 'ਗਾ', 'ਗੀ', 'ਗੇ', 'ਇਹ', 'ਉਹ', 'ਇਸ', 'ਉਨ੍ਹਾਂ',
      'ਕਿ', 'ਜੋ', 'ਜਿਸ', 'ਜਿਨ੍ਹਾਂ', 'ਕੀ', 'ਕਿਉਂ', 'ਕਿਵੇਂ', 'ਕਦੋਂ', 'ਕਿੱਥੇ', 'ਕੌਣ', 'ਕੁਝ',
      'ਕੋਈ', 'ਸਭ', 'ਵੀ', 'ਤਾਂ', 'ਹੀ', 'ਨਹੀਂ', 'ਨਾ', 'ਤੇ', 'ਲਈ', 'ਨਾਲ', 'ਬਾਅਦ', 'ਪਹਿਲਾਂ'
    ]));
  }

  private initializeTopicCategories(): void {
    const categories = {
      'technology': ['computer', 'software', 'internet', 'ai', 'programming', 'کمپیوٹر', 'سافٹ ویئر', 'ਕੰਪਿਊਟਰ'],
      'health': ['health', 'medicine', 'doctor', 'hospital', 'صحت', 'دوا', 'ڈاکٹر', 'ਸਿਹਤ', 'ਦਵਾਈ'],
      'education': ['school', 'university', 'study', 'learn', 'سکول', 'یونیورسٹی', 'پڑھائی', 'ਸਕੂਲ', 'ਪੜ੍ਹਾਈ'],
      'family': ['family', 'mother', 'father', 'brother', 'sister', 'خاندان', 'ماں', 'باپ', 'ਪਰਿਵਾਰ', 'ਮਾਂ'],
      'work': ['job', 'work', 'office', 'business', 'کام', 'نوکری', 'آفس', 'ਕੰਮ', 'ਨੌਕਰੀ'],
      'food': ['food', 'eat', 'cook', 'restaurant', 'کھانا', 'کھانے', 'ریسٹورنٹ', 'ਖਾਣਾ', 'ਰੈਸਟੋਰੈਂਟ'],
      'travel': ['travel', 'trip', 'vacation', 'سفر', 'سیاحت', 'چھٹیاں', 'ਸਫ਼ਰ', 'ਯਾਤਰਾ'],
      'entertainment': ['movie', 'music', 'game', 'فلم', 'موسیقی', 'کھیل', 'ਫਿਲਮ', 'ਸੰਗੀਤ'],
      'sports': ['football', 'cricket', 'tennis', 'کرکٹ', 'فٹ بال', 'ਕ੍ਰਿਕੇਟ', 'ਫੁੱਟਬਾਲ'],
      'weather': ['weather', 'rain', 'sun', 'موسم', 'بارش', 'دھوپ', 'ਮੌਸਮ', 'ਮੀਂਹ']
    };

    for (const [category, keywords] of Object.entries(categories)) {
      this.topicCategories.set(category, keywords);
    }
  }

  // Main topic extraction function
  extractTopics(text: string, language: string = 'en'): TopicResult {
    const preprocessedText = this.preprocessText(text, language);
    const keywords = this.extractKeywords(preprocessedText, language);
    const topics = this.identifyTopics(keywords, language);
    const confidence = this.calculateTopicConfidence(topics, keywords);
    const coherence = this.calculateTopicCoherence(topics);

    return {
      topics,
      confidence,
      language,
      coherence
    };
  }

  // Extract topics from multiple documents
  extractTopicsFromDocuments(documents: string[], language: string = 'en'): DocumentTopics[] {
    const results: DocumentTopics[] = [];
    
    // Add documents to TF-IDF
    documents.forEach(doc => {
      const preprocessed = this.preprocessText(doc, language);
      this.tfidf.addDocument(preprocessed);
    });

    documents.forEach((doc, index) => {
      const topicResult = this.extractTopics(doc, language);
      const topicDistribution = this.calculateTopicDistribution(topicResult.topics);
      const primaryTopic = topicResult.topics[0] || this.createDefaultTopic();

      results.push({
        documentId: `doc_${index}`,
        topics: topicResult.topics,
        primaryTopic,
        topicDistribution
      });
    });

    return results;
  }

  // Cluster topics
  clusterTopics(topics: Topic[], numClusters: number = 5): TopicCluster[] {
    if (topics.length === 0) return [];

    // Simple k-means clustering based on keyword similarity
    const clusters: TopicCluster[] = [];
    const topicVectors = topics.map(topic => this.topicToVector(topic));
    
    // Initialize centroids randomly
    const centroids: number[][] = [];
    for (let i = 0; i < numClusters; i++) {
      const randomTopic = topics[Math.floor(Math.random() * topics.length)];
      centroids.push(this.topicToVector(randomTopic));
    }

    // Assign topics to clusters
    const assignments: number[] = new Array(topics.length);
    
    for (let iter = 0; iter < 10; iter++) {
      // Assign each topic to nearest centroid
      for (let i = 0; i < topics.length; i++) {
        let minDistance = Infinity;
        let bestCluster = 0;
        
        for (let j = 0; j < centroids.length; j++) {
          const distance = this.calculateDistance(topicVectors[i], centroids[j]);
          if (distance < minDistance) {
            minDistance = distance;
            bestCluster = j;
          }
        }
        
        assignments[i] = bestCluster;
      }

      // Update centroids
      for (let j = 0; j < centroids.length; j++) {
        const clusterTopics = topics.filter((_, i) => assignments[i] === j);
        if (clusterTopics.length > 0) {
          centroids[j] = this.calculateCentroid(clusterTopics.map(t => this.topicToVector(t)));
        }
      }
    }

    // Create cluster objects
    for (let i = 0; i < numClusters; i++) {
      const clusterTopics = topics.filter((_, j) => assignments[j] === i);
      if (clusterTopics.length > 0) {
        clusters.push({
          clusterId: `cluster_${i}`,
          topics: clusterTopics,
          centroid: this.vectorToKeywords(centroids[i]),
          coherence: this.calculateClusterCoherence(clusterTopics),
          size: clusterTopics.length
        });
      }
    }

    return clusters.sort((a, b) => b.size - a.size);
  }

  private preprocessText(text: string, language: string): string {
    // Convert to lowercase
    let processed = text.toLowerCase();
    
    // Remove punctuation and special characters
    processed = processed.replace(/[^\w\s\u0600-\u06FF\u0A00-\u0A7F]/g, ' ');
    
    // Remove extra whitespace
    processed = processed.replace(/\s+/g, ' ').trim();
    
    return processed;
  }

  private extractKeywords(text: string, language: string): string[] {
    const words = text.split(/\s+/);
    const stopWordsSet = this.stopWords.get(language) || new Set();
    
    // Filter out stopwords and short words
    const filteredWords = words.filter(word => 
      word.length > 2 && !stopWordsSet.has(word)
    );

    // For English, apply stemming
    const processedWords = language === 'en' ? 
      filteredWords.map(word => this.stemmer.stem(word)) : 
      filteredWords;

    // Count word frequencies
    const wordCounts = new Map<string, number>();
    processedWords.forEach(word => {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
    });

    // Sort by frequency and return top keywords
    return Array.from(wordCounts.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([word]) => word);
  }

  private identifyTopics(keywords: string[], language: string): Topic[] {
    const topics: Topic[] = [];
    const usedKeywords = new Set<string>();

    // Group keywords by categories
    for (const [category, categoryKeywords] of this.topicCategories.entries()) {
      const matchingKeywords = keywords.filter(keyword => 
        categoryKeywords.some(catKeyword => 
          keyword.includes(catKeyword.toLowerCase()) || 
          catKeyword.toLowerCase().includes(keyword)
        ) && !usedKeywords.has(keyword)
      );

      if (matchingKeywords.length > 0) {
        const weight = matchingKeywords.length / keywords.length;
        const confidence = Math.min(0.9, weight * 2);
        
        topics.push({
          name: category,
          keywords: matchingKeywords,
          weight,
          confidence,
          category,
          relatedTopics: []
        });

        matchingKeywords.forEach(kw => usedKeywords.add(kw));
      }
    }

    // Create topics for remaining keywords
    const remainingKeywords = keywords.filter(kw => !usedKeywords.has(kw));
    if (remainingKeywords.length > 0) {
      // Group similar keywords
      const groups = this.groupSimilarKeywords(remainingKeywords);
      
      groups.forEach((group, index) => {
        if (group.length >= 2) {
          const topicName = group[0]; // Use first keyword as topic name
          const weight = group.length / keywords.length;
          
          topics.push({
            name: topicName,
            keywords: group,
            weight,
            confidence: Math.min(0.7, weight * 1.5),
            category: 'general',
            relatedTopics: []
          });
        }
      });
    }

    // Sort topics by weight and add related topics
    topics.sort((a, b) => b.weight - a.weight);
    this.addRelatedTopics(topics);

    return topics.slice(0, 10); // Return top 10 topics
  }

  private groupSimilarKeywords(keywords: string[]): string[][] {
    const groups: string[][] = [];
    const used = new Set<string>();

    for (const keyword of keywords) {
      if (used.has(keyword)) continue;

      const group = [keyword];
      used.add(keyword);

      for (const otherKeyword of keywords) {
        if (used.has(otherKeyword)) continue;
        
        // Simple similarity check based on common characters
        const similarity = this.calculateStringSimilarity(keyword, otherKeyword);
        if (similarity > 0.6) {
          group.push(otherKeyword);
          used.add(otherKeyword);
        }
      }

      groups.push(group);
    }

    return groups;
  }

  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = natural.LevenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  private addRelatedTopics(topics: Topic[]): void {
    for (let i = 0; i < topics.length; i++) {
      const currentTopic = topics[i];
      const relatedTopics: string[] = [];

      for (let j = 0; j < topics.length; j++) {
        if (i === j) continue;
        
        const otherTopic = topics[j];
        const commonKeywords = currentTopic.keywords.filter(kw => 
          otherTopic.keywords.includes(kw)
        );

        if (commonKeywords.length > 0) {
          relatedTopics.push(otherTopic.name);
        }
      }

      currentTopic.relatedTopics = relatedTopics.slice(0, 3);
    }
  }

  private calculateTopicConfidence(topics: Topic[], keywords: string[]): number {
    if (topics.length === 0) return 0;
    
    const totalWeight = topics.reduce((sum, topic) => sum + topic.weight, 0);
    const avgConfidence = topics.reduce((sum, topic) => sum + topic.confidence, 0) / topics.length;
    
    return Math.min(0.95, (totalWeight + avgConfidence) / 2);
  }

  private calculateTopicCoherence(topics: Topic[]): number {
    if (topics.length === 0) return 0;
    
    let coherenceSum = 0;
    let pairCount = 0;

    for (let i = 0; i < topics.length; i++) {
      for (let j = i + 1; j < topics.length; j++) {
        const topic1 = topics[i];
        const topic2 = topics[j];
        
        const commonKeywords = topic1.keywords.filter(kw => 
          topic2.keywords.includes(kw)
        );
        
        const coherence = commonKeywords.length / 
          Math.max(topic1.keywords.length, topic2.keywords.length);
        
        coherenceSum += coherence;
        pairCount++;
      }
    }

    return pairCount > 0 ? coherenceSum / pairCount : 0.5;
  }

  private calculateTopicDistribution(topics: Topic[]): { [topicName: string]: number } {
    const distribution: { [topicName: string]: number } = {};
    const totalWeight = topics.reduce((sum, topic) => sum + topic.weight, 0);
    
    topics.forEach(topic => {
      distribution[topic.name] = totalWeight > 0 ? topic.weight / totalWeight : 0;
    });
    
    return distribution;
  }

  private topicToVector(topic: Topic): number[] {
    // Simple vector representation based on keyword frequencies
    const vector = new Array(100).fill(0);
    topic.keywords.forEach((keyword, index) => {
      if (index < vector.length) {
        vector[index] = topic.weight;
      }
    });
    return vector;
  }

  private calculateDistance(vec1: number[], vec2: number[]): number {
    let sum = 0;
    for (let i = 0; i < Math.min(vec1.length, vec2.length); i++) {
      sum += Math.pow(vec1[i] - vec2[i], 2);
    }
    return Math.sqrt(sum);
  }

  private calculateCentroid(vectors: number[][]): number[] {
    if (vectors.length === 0) return [];
    
    const centroid = new Array(vectors[0].length).fill(0);
    vectors.forEach(vector => {
      vector.forEach((value, index) => {
        centroid[index] += value;
      });
    });
    
    return centroid.map(value => value / vectors.length);
  }

  private vectorToKeywords(vector: number[]): string[] {
    return vector.map((value, index) => `keyword_${index}`).slice(0, 5);
  }

  private calculateClusterCoherence(topics: Topic[]): number {
    if (topics.length <= 1) return 1;
    
    let coherenceSum = 0;
    let pairCount = 0;

    for (let i = 0; i < topics.length; i++) {
      for (let j = i + 1; j < topics.length; j++) {
        const similarity = this.calculateTopicSimilarity(topics[i], topics[j]);
        coherenceSum += similarity;
        pairCount++;
      }
    }

    return pairCount > 0 ? coherenceSum / pairCount : 0;
  }

  private calculateTopicSimilarity(topic1: Topic, topic2: Topic): number {
    const commonKeywords = topic1.keywords.filter(kw => 
      topic2.keywords.includes(kw)
    );
    
    const totalKeywords = new Set([...topic1.keywords, ...topic2.keywords]).size;
    return totalKeywords > 0 ? commonKeywords.length / totalKeywords : 0;
  }

  private createDefaultTopic(): Topic {
    return {
      name: 'general',
      keywords: [],
      weight: 0.1,
      confidence: 0.3,
      category: 'general',
      relatedTopics: []
    };
  }

  // Get topic trends over time
  getTopicTrends(documents: { text: string; timestamp: Date; language: string }[]): Map<string, number[]> {
    const trends = new Map<string, number[]>();
    const timeWindows = this.createTimeWindows(documents);

    timeWindows.forEach(window => {
      const allTopics = window.flatMap(doc =>
        this.extractTopics(doc.text, doc.language).topics
      );

      const topicCounts = new Map<string, number>();
      allTopics.forEach(topic => {
        topicCounts.set(topic.name, (topicCounts.get(topic.name) || 0) + topic.weight);
      });

      for (const [topicName, count] of topicCounts.entries()) {
        if (!trends.has(topicName)) {
          trends.set(topicName, []);
        }
        trends.get(topicName)!.push(count);
      }
    });

    return trends;
  }

  private createTimeWindows(documents: { text: string; timestamp: Date; language: string }[]): Array<Array<{ text: string; timestamp: Date; language: string }>> {
    // Simple implementation: group by day
    const windows = new Map<string, Array<{ text: string; timestamp: Date; language: string }>>();

    documents.forEach(doc => {
      const dateKey = doc.timestamp.toDateString();
      if (!windows.has(dateKey)) {
        windows.set(dateKey, []);
      }
      windows.get(dateKey)!.push(doc);
    });

    return Array.from(windows.values());
  }
}
