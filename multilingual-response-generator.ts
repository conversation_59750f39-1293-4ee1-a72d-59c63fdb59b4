// Multilingual Response Generator for Chitu00
// Generates culturally appropriate responses in English, Urdu, and Punjabi

import { PersonalityTraits } from './memory-system';
import { MultilingualProcessor, MultilingualAnalysis, LanguageCapability } from './multilingual-processor';

export interface MultilingualResponseContext {
  userMessage: string;
  detectedLanguage: string;
  analysis: MultilingualAnalysis;
  personality: PersonalityTraits;
  mood: string;
  culturalContext: any;
}

export interface MultilingualResponse {
  content: string;
  language: string;
  script: string;
  direction: 'ltr' | 'rtl';
  confidence: number;
  culturalAdaptation: string[];
  personalityMarkers: string[];
}

export interface CulturalPersonality {
  language: string;
  formalityLevel: number;     // Urdu: high, Punjabi: moderate
  emotionalExpression: number; // Cultural norms for emotion
  humorStyle: string;         // Language-specific humor
  respectPatterns: string[];  // Cultural respect expressions
}

export const CULTURAL_PERSONALITIES: Record<string, CulturalPersonality> = {
  en: {
    language: 'en',
    formalityLevel: 5,
    emotionalExpression: 6,
    humorStyle: 'direct',
    respectPatterns: ['please', 'thank you', 'appreciate']
  },
  ur: {
    language: 'ur',
    formalityLevel: 8,
    emotionalExpression: 5,
    humorStyle: 'poetic',
    respectPatterns: ['آپ', 'جناب', 'محترم', 'شکریہ']
  },
  pa: {
    language: 'pa',
    formalityLevel: 6,
    emotionalExpression: 7,
    humorStyle: 'expressive',
    respectPatterns: ['ਜੀ', 'ਸਤਿਕਾਰਯੋਗ', 'ਧੰਨਵਾਦ']
  }
};

export class MultilingualResponseGenerator {
  private processor: MultilingualProcessor;
  private responseTemplates: Map<string, ResponseTemplates>;

  constructor() {
    this.processor = new MultilingualProcessor();
    this.initializeResponseTemplates();
  }

  private initializeResponseTemplates(): void {
    this.responseTemplates = new Map();

    // English templates
    this.responseTemplates.set('en', {
      greetings: {
        formal: ['Hello! How may I assist you today?', 'Good day! How can I help you?'],
        casual: ['Hi there!', 'Hey! What\'s up?', 'Hello! Nice to see you!'],
        curious: ['Hello! I\'m excited to chat with you!', 'Hi! What interesting topics shall we explore?']
      },
      questions: {
        encouraging: ['That\'s a great question!', 'I love your curiosity!', 'Interesting question!'],
        thoughtful: ['Let me think about that...', 'That\'s worth considering...', 'Good point to explore...'],
        supportive: ['I\'m here to help you understand this.', 'Let\'s work through this together.']
      },
      emotions: {
        positive: ['I\'m so happy to hear that!', 'That\'s wonderful!', 'Your positivity is contagious!'],
        negative: ['I understand this is difficult.', 'I\'m here to support you.', 'That sounds challenging.'],
        curious: ['Your curiosity inspires me!', 'I love exploring ideas with you!']
      },
      personality: {
        creative: ['That sparks some creative ideas!', 'I\'m imagining interesting possibilities!'],
        empathetic: ['I can understand how you feel.', 'That must be meaningful to you.'],
        humorous: ['That made me smile!', 'I like your sense of humor!']
      }
    });

    // Urdu templates
    this.responseTemplates.set('ur', {
      greetings: {
        formal: ['السلام علیکم! آج میں آپ کی کیسے مدد کر سکتا ہوں؟', 'آداب! کیا حال ہے؟'],
        casual: ['ہیلو! کیا حال ہے؟', 'سلام! کیسے ہیں آپ؟'],
        curious: ['سلام! میں آپ سے بات کرنے کے لیے بہت پرجوش ہوں!']
      },
      questions: {
        encouraging: ['بہت اچھا سوال!', 'آپ کا تجسس قابل تعریف ہے!', 'دلچسپ سوال!'],
        thoughtful: ['اس پر غور کرتے ہیں...', 'یہ سوچنے کی بات ہے...', 'اچھا نکتہ ہے...'],
        supportive: ['میں آپ کی مدد کے لیے حاضر ہوں۔', 'آئیے مل کر اس کا حل نکالتے ہیں۔']
      },
      emotions: {
        positive: ['یہ سن کر بہت خوشی ہوئی!', 'واہ! یہ تو بہت اچھی بات ہے!', 'آپ کی خوشی دیکھ کر دل خوش ہو گیا!'],
        negative: ['میں سمجھ سکتا ہوں یہ مشکل ہے۔', 'میں آپ کے ساتھ ہوں۔', 'یہ واقعی پریشان کن ہے۔'],
        curious: ['آپ کا تجسس بہت اچھا ہے!', 'میں آپ کے ساتھ نئے خیالات کی تلاش کرنا پسند کرتا ہوں!']
      },
      personality: {
        creative: ['یہ کچھ تخلیقی خیالات لاتا ہے!', 'میں دلچسپ امکانات کا تصور کر رہا ہوں!'],
        empathetic: ['میں آپ کے احساسات کو سمجھ سکتا ہوں۔', 'یہ آپ کے لیے اہم ہونا چاہیے۔'],
        humorous: ['اس سے مجھے مسکراہٹ آئی!', 'آپ کا مزاح پسند ہے!']
      }
    });

    // Punjabi templates
    this.responseTemplates.set('pa', {
      greetings: {
        formal: ['ਸਤ ਸ੍ਰੀ ਅਕਾਲ! ਅੱਜ ਮੈਂ ਤੁਹਾਡੀ ਕਿਵੇਂ ਮਦਦ ਕਰ ਸਕਦਾ ਹਾਂ?', 'ਨਮਸਕਾਰ! ਕੀ ਹਾਲ ਹੈ?'],
        casual: ['ਸਤ ਸ੍ਰੀ ਅਕਾਲ! ਕੀ ਹਾਲ ਹੈ?', 'ਹੈਲੋ! ਕਿਵੇਂ ਹੋ?'],
        curious: ['ਸਤ ਸ੍ਰੀ ਅਕਾਲ! ਮੈਂ ਤੁਹਾਡੇ ਨਾਲ ਗੱਲ ਕਰਨ ਲਈ ਬਹੁਤ ਉਤਸੁਕ ਹਾਂ!']
      },
      questions: {
        encouraging: ['ਬਹੁਤ ਵਧੀਆ ਸਵਾਲ!', 'ਤੁਹਾਡੀ ਉਤਸੁਕਤਾ ਚੰਗੀ ਹੈ!', 'ਦਿਲਚਸਪ ਸਵਾਲ!'],
        thoughtful: ['ਇਸ ਬਾਰੇ ਸੋਚਦੇ ਹਾਂ...', 'ਇਹ ਸੋਚਣ ਵਾਲੀ ਗੱਲ ਹੈ...', 'ਚੰਗਾ ਨੁਕਤਾ ਹੈ...'],
        supportive: ['ਮੈਂ ਤੁਹਾਡੀ ਮਦਦ ਲਈ ਹਾਜ਼ਰ ਹਾਂ।', 'ਆਓ ਮਿਲ ਕੇ ਇਸ ਦਾ ਹੱਲ ਲੱਭਦੇ ਹਾਂ।']
      },
      emotions: {
        positive: ['ਇਹ ਸੁਣ ਕੇ ਬਹੁਤ ਖੁਸ਼ੀ ਹੋਈ!', 'ਵਾਹ! ਇਹ ਤਾਂ ਬਹੁਤ ਚੰਗੀ ਗੱਲ ਹੈ!', 'ਤੁਹਾਡੀ ਖੁਸ਼ੀ ਦੇਖ ਕੇ ਦਿਲ ਖੁਸ਼ ਹੋ ਗਿਆ!'],
        negative: ['ਮੈਂ ਸਮਝ ਸਕਦਾ ਹਾਂ ਇਹ ਮੁਸ਼ਕਲ ਹੈ।', 'ਮੈਂ ਤੁਹਾਡੇ ਨਾਲ ਹਾਂ।', 'ਇਹ ਸੱਚਮੁੱਚ ਪਰੇਸ਼ਾਨ ਕਰਨ ਵਾਲੀ ਗੱਲ ਹੈ।'],
        curious: ['ਤੁਹਾਡੀ ਉਤਸੁਕਤਾ ਬਹੁਤ ਚੰਗੀ ਹੈ!', 'ਮੈਂ ਤੁਹਾਡੇ ਨਾਲ ਨਵੇਂ ਵਿਚਾਰਾਂ ਦੀ ਖੋਜ ਕਰਨਾ ਪਸੰਦ ਕਰਦਾ ਹਾਂ!']
      },
      personality: {
        creative: ['ਇਹ ਕੁਝ ਰਚਨਾਤਮਕ ਵਿਚਾਰ ਲਿਆਉਂਦਾ ਹੈ!', 'ਮੈਂ ਦਿਲਚਸਪ ਸੰਭਾਵਨਾਵਾਂ ਦਾ ਤਸੱਵਰ ਕਰ ਰਿਹਾ ਹਾਂ!'],
        empathetic: ['ਮੈਂ ਤੁਹਾਡੇ ਭਾਵਨਾਵਾਂ ਨੂੰ ਸਮਝ ਸਕਦਾ ਹਾਂ।', 'ਇਹ ਤੁਹਾਡੇ ਲਈ ਮਹੱਤਵਪੂਰਨ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ।'],
        humorous: ['ਇਸ ਨਾਲ ਮੈਨੂੰ ਮੁਸਕਰਾਹਟ ਆਈ!', 'ਤੁਹਾਡਾ ਮਜ਼ਾਕ ਪਸੰਦ ਹੈ!']
      }
    });
  }

  // Main response generation function
  generateMultilingualResponse(context: MultilingualResponseContext): MultilingualResponse {
    const { analysis, personality, mood } = context;
    const language = analysis.detectedLanguage.language;
    const templates = this.responseTemplates.get(language) || this.responseTemplates.get('en')!;
    
    let baseResponse = '';
    const culturalAdaptation: string[] = [];
    const personalityMarkers: string[] = [];

    // Generate base response based on message type
    if (analysis.culturalMarkers.includes('greeting')) {
      baseResponse = this.generateGreetingResponse(templates, personality, mood);
      culturalAdaptation.push('greeting_response');
    } else if (analysis.originalText.includes('?')) {
      baseResponse = this.generateQuestionResponse(templates, personality, analysis);
      culturalAdaptation.push('question_response');
    } else if (analysis.emotion !== 'neutral') {
      baseResponse = this.generateEmotionalResponse(templates, personality, analysis.emotion);
      culturalAdaptation.push('emotional_response');
    } else {
      baseResponse = this.generatePersonalityResponse(templates, personality, analysis);
      culturalAdaptation.push('personality_response');
    }

    // Apply cultural adaptations
    baseResponse = this.applyCulturalAdaptations(baseResponse, language, analysis, culturalAdaptation);
    
    // Apply personality modifications
    baseResponse = this.applyPersonalityModifications(baseResponse, personality, language, personalityMarkers);

    // Calculate confidence
    const confidence = this.calculateResponseConfidence(analysis, personality, language);

    const languageInfo = this.processor.getLanguageInfo(language);

    return {
      content: baseResponse,
      language,
      script: languageInfo?.script || 'Latin',
      direction: languageInfo?.direction || 'ltr',
      confidence,
      culturalAdaptation,
      personalityMarkers
    };
  }

  private generateGreetingResponse(templates: ResponseTemplates, personality: PersonalityTraits, mood: string): string {
    let responseType: keyof typeof templates.greetings = 'casual';
    
    if (personality.conscientiousness > 0.7) responseType = 'formal';
    if (personality.curiosity > 0.8 || mood === 'curious') responseType = 'curious';
    
    const responses = templates.greetings[responseType];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  private generateQuestionResponse(templates: ResponseTemplates, personality: PersonalityTraits, analysis: MultilingualAnalysis): string {
    let responseType: keyof typeof templates.questions = 'thoughtful';
    
    if (personality.agreeableness > 0.8) responseType = 'supportive';
    if (personality.curiosity > 0.8) responseType = 'encouraging';
    
    const responses = templates.questions[responseType];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  private generateEmotionalResponse(templates: ResponseTemplates, personality: PersonalityTraits, emotion: string): string {
    const emotionType = emotion as keyof typeof templates.emotions;
    const responses = templates.emotions[emotionType] || templates.emotions.positive;
    return responses[Math.floor(Math.random() * responses.length)];
  }

  private generatePersonalityResponse(templates: ResponseTemplates, personality: PersonalityTraits, analysis: MultilingualAnalysis): string {
    let responseType: keyof typeof templates.personality = 'empathetic';
    
    if (personality.creativity > 0.8) responseType = 'creative';
    if (personality.humor > 0.7) responseType = 'humorous';
    if (personality.empathy > 0.8) responseType = 'empathetic';
    
    const responses = templates.personality[responseType];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  private applyCulturalAdaptations(response: string, language: string, analysis: MultilingualAnalysis, adaptations: string[]): string {
    let adaptedResponse = response;
    
    // Add cultural politeness markers based on formality
    if (analysis.formalityLevel > 7) {
      const politenessMarkers = {
        en: ['please', 'kindly'],
        ur: ['برائے کرم', 'مہربانی سے'],
        pa: ['ਕਿਰਪਾ ਕਰਕੇ', 'ਮਿਹਰਬਾਨੀ ਨਾਲ']
      };
      
      const markers = politenessMarkers[language as keyof typeof politenessMarkers];
      if (markers && Math.random() < 0.3) {
        const marker = markers[Math.floor(Math.random() * markers.length)];
        adaptedResponse = `${marker} ${adaptedResponse}`;
        adaptations.push('politeness_added');
      }
    }

    // Add respectful address for Urdu/Punjabi
    if ((language === 'ur' || language === 'pa') && analysis.culturalMarkers.includes('respectful')) {
      const respectfulAddresses = {
        ur: ['جناب', 'آپ'],
        pa: ['ਜੀ', 'ਸਾਹਿਬ']
      };
      
      const addresses = respectfulAddresses[language as keyof typeof respectfulAddresses];
      if (addresses && Math.random() < 0.4) {
        const address = addresses[Math.floor(Math.random() * addresses.length)];
        adaptedResponse = `${address}، ${adaptedResponse}`;
        adaptations.push('respectful_address');
      }
    }

    return adaptedResponse;
  }

  private applyPersonalityModifications(response: string, personality: PersonalityTraits, language: string, markers: string[]): string {
    let modifiedResponse = response;
    
    // Add humor if high humor trait
    if (personality.humor > 0.7 && Math.random() < 0.3) {
      const humorAdditions = {
        en: [' 😊', ' (with a smile!)', ' - that made me chuckle!'],
        ur: [' 😊', ' (مسکراتے ہوئے!)', ' - یہ مجھے ہنسا گیا!'],
        pa: [' 😊', ' (ਮੁਸਕਰਾਉਂਦੇ ਹੋਏ!)', ' - ਇਸ ਨਾਲ ਮੈਂ ਹੱਸ ਪਿਆ!']
      };
      
      const additions = humorAdditions[language as keyof typeof humorAdditions] || humorAdditions.en;
      const addition = additions[Math.floor(Math.random() * additions.length)];
      modifiedResponse += addition;
      markers.push('humor_added');
    }

    // Add curiosity questions if high curiosity
    if (personality.curiosity > 0.8 && Math.random() < 0.4) {
      const curiosityAdditions = {
        en: [' What do you think?', ' I\'m curious about your perspective!', ' Tell me more!'],
        ur: [' آپ کا کیا خیال ہے؟', ' میں آپ کے نقطہ نظر کے بارے میں جاننا چاہتا ہوں!', ' مزید بتائیے!'],
        pa: [' ਤੁਸੀਂ ਕੀ ਸੋਚਦੇ ਹੋ?', ' ਮੈਂ ਤੁਹਾਡੇ ਨਜ਼ਰੀਏ ਬਾਰੇ ਜਾਣਨਾ ਚਾਹੁੰਦਾ ਹਾਂ!', ' ਹੋਰ ਦੱਸੋ!']
      };
      
      const additions = curiosityAdditions[language as keyof typeof curiosityAdditions] || curiosityAdditions.en;
      const addition = additions[Math.floor(Math.random() * additions.length)];
      modifiedResponse += addition;
      markers.push('curiosity_added');
    }

    return modifiedResponse;
  }

  private calculateResponseConfidence(analysis: MultilingualAnalysis, personality: PersonalityTraits, language: string): number {
    let confidence = 0.7; // Base confidence
    
    // Increase confidence for supported languages
    if (['en', 'ur', 'pa'].includes(language)) {
      confidence += 0.1;
    }
    
    // Increase confidence for clear cultural markers
    if (analysis.culturalMarkers.length > 0) {
      confidence += 0.1;
    }
    
    // Increase confidence for high personality traits
    if (personality.empathy > 0.8) confidence += 0.05;
    if (personality.agreeableness > 0.8) confidence += 0.05;
    
    // Decrease confidence for complex or ambiguous text
    if (analysis.detectedLanguage.confidence < 0.7) {
      confidence -= 0.1;
    }
    
    return Math.max(0.3, Math.min(1.0, confidence));
  }
}

// Response template interface
interface ResponseTemplates {
  greetings: {
    formal: string[];
    casual: string[];
    curious: string[];
  };
  questions: {
    encouraging: string[];
    thoughtful: string[];
    supportive: string[];
  };
  emotions: {
    positive: string[];
    negative: string[];
    curious: string[];
  };
  personality: {
    creative: string[];
    empathetic: string[];
    humorous: string[];
  };
}


