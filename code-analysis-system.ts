// Code Analysis and Optimization System for Chitu00
// Analyzes code quality, performance, and suggests improvements

import * as acorn from 'acorn';
import * as walk from 'acorn-walk';
import * as esprima from 'esprima';
import * as escodegen from 'escodegen';

export interface CodeAnalysisResult {
  file: string;
  issues: CodeIssue[];
  metrics: CodeMetrics;
  suggestions: OptimizationSuggestion[];
  complexity: ComplexityMetrics;
}

export interface CodeIssue {
  type: 'performance' | 'maintainability' | 'security' | 'style';
  severity: 'low' | 'medium' | 'high' | 'critical';
  line: number;
  column: number;
  message: string;
  rule: string;
  fixable: boolean;
  suggestedFix?: string;
}

export interface CodeMetrics {
  linesOfCode: number;
  cyclomaticComplexity: number;
  maintainabilityIndex: number;
  duplicatedLines: number;
  testCoverage: number;
  technicalDebt: number; // in minutes
}

export interface OptimizationSuggestion {
  id: string;
  type: 'performance' | 'memory' | 'readability' | 'architecture';
  priority: 'low' | 'medium' | 'high';
  description: string;
  currentCode: string;
  optimizedCode: string;
  expectedBenefit: string;
  estimatedEffort: number; // in hours
  riskLevel: 'low' | 'medium' | 'high';
}

export interface ComplexityMetrics {
  cyclomaticComplexity: number;
  cognitiveComplexity: number;
  nestingDepth: number;
  functionLength: number;
  parameterCount: number;
}

export class CodeAnalysisSystem {
  private analysisCache: Map<string, CodeAnalysisResult> = new Map();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  // Main code analysis entry point
  async analyzeCode(filePath: string, sourceCode: string): Promise<CodeAnalysisResult> {
    const cacheKey = `${filePath}-${this.hashCode(sourceCode)}`;
    
    // Check cache first
    const cached = this.analysisCache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      return cached;
    }

    try {
      const result: CodeAnalysisResult = {
        file: filePath,
        issues: [],
        metrics: await this.calculateMetrics(sourceCode),
        suggestions: [],
        complexity: await this.calculateComplexity(sourceCode)
      };

      // Perform various analyses
      result.issues = await this.findCodeIssues(sourceCode);
      result.suggestions = await this.generateOptimizationSuggestions(sourceCode, result.metrics);

      // Cache the result
      this.analysisCache.set(cacheKey, result);

      return result;
    } catch (error) {
      console.error(`Error analyzing code for ${filePath}:`, error);
      return {
        file: filePath,
        issues: [{
          type: 'performance',
          severity: 'medium',
          line: 0,
          column: 0,
          message: `Code analysis failed: ${error}`,
          rule: 'analysis-error',
          fixable: false
        }],
        metrics: this.getDefaultMetrics(),
        suggestions: [],
        complexity: this.getDefaultComplexity()
      };
    }
  }

  // Calculate code metrics
  private async calculateMetrics(sourceCode: string): Promise<CodeMetrics> {
    try {
      const ast = esprima.parseScript(sourceCode, { tolerant: true, range: true });
      
      const lines = sourceCode.split('\n');
      const linesOfCode = lines.filter(line => line.trim() && !line.trim().startsWith('//')).length;
      
      let cyclomaticComplexity = 1; // Base complexity
      let functionCount = 0;

      // Walk the AST to calculate complexity
      walk.simple(ast as any, {
        IfStatement: () => cyclomaticComplexity++,
        WhileStatement: () => cyclomaticComplexity++,
        ForStatement: () => cyclomaticComplexity++,
        SwitchCase: () => cyclomaticComplexity++,
        ConditionalExpression: () => cyclomaticComplexity++,
        LogicalExpression: (node: any) => {
          if (node.operator === '&&' || node.operator === '||') {
            cyclomaticComplexity++;
          }
        },
        FunctionDeclaration: () => functionCount++,
        FunctionExpression: () => functionCount++,
        ArrowFunctionExpression: () => functionCount++
      });

      // Calculate maintainability index (simplified version)
      const maintainabilityIndex = Math.max(0, 
        171 - 5.2 * Math.log(linesOfCode) - 0.23 * cyclomaticComplexity - 16.2 * Math.log(linesOfCode)
      );

      // Detect duplicated code patterns (simplified)
      const duplicatedLines = this.detectDuplicatedCode(sourceCode);

      return {
        linesOfCode,
        cyclomaticComplexity,
        maintainabilityIndex,
        duplicatedLines,
        testCoverage: 0, // Would need actual test data
        technicalDebt: Math.max(0, (cyclomaticComplexity - 10) * 5 + duplicatedLines * 2)
      };
    } catch (error) {
      return this.getDefaultMetrics();
    }
  }

  // Calculate complexity metrics
  private async calculateComplexity(sourceCode: string): Promise<ComplexityMetrics> {
    try {
      const ast = esprima.parseScript(sourceCode, { tolerant: true });
      
      let maxNestingDepth = 0;
      let maxFunctionLength = 0;
      let maxParameterCount = 0;
      let cyclomaticComplexity = 1;
      let cognitiveComplexity = 0;

      const analyzeFunction = (node: any, depth = 0) => {
        maxNestingDepth = Math.max(maxNestingDepth, depth);
        
        if (node.type === 'FunctionDeclaration' || node.type === 'FunctionExpression') {
          const functionLines = node.loc ? node.loc.end.line - node.loc.start.line : 0;
          maxFunctionLength = Math.max(maxFunctionLength, functionLines);
          
          if (node.params) {
            maxParameterCount = Math.max(maxParameterCount, node.params.length);
          }
        }
      };

      walk.recursive(ast as any, null, {
        IfStatement: (node: any, state: any, c: any) => {
          cyclomaticComplexity++;
          cognitiveComplexity++;
          c(node.consequent, state + 1);
          if (node.alternate) c(node.alternate, state + 1);
        },
        WhileStatement: (node: any, state: any, c: any) => {
          cyclomaticComplexity++;
          cognitiveComplexity++;
          c(node.body, state + 1);
        },
        ForStatement: (node: any, state: any, c: any) => {
          cyclomaticComplexity++;
          cognitiveComplexity++;
          c(node.body, state + 1);
        },
        FunctionDeclaration: analyzeFunction,
        FunctionExpression: analyzeFunction
      });

      return {
        cyclomaticComplexity,
        cognitiveComplexity,
        nestingDepth: maxNestingDepth,
        functionLength: maxFunctionLength,
        parameterCount: maxParameterCount
      };
    } catch (error) {
      return this.getDefaultComplexity();
    }
  }

  // Find code issues and problems
  private async findCodeIssues(sourceCode: string): Promise<CodeIssue[]> {
    const issues: CodeIssue[] = [];
    const lines = sourceCode.split('\n');

    // Check for common performance issues
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Detect potential performance issues
      if (line.includes('console.log') && !line.includes('//')) {
        issues.push({
          type: 'performance',
          severity: 'low',
          line: lineNumber,
          column: line.indexOf('console.log'),
          message: 'Console.log statements can impact performance in production',
          rule: 'no-console',
          fixable: true,
          suggestedFix: 'Remove or replace with proper logging'
        });
      }

      // Detect synchronous operations that could be async
      if (line.includes('JSON.parse') && !line.includes('await') && !line.includes('try')) {
        issues.push({
          type: 'performance',
          severity: 'medium',
          line: lineNumber,
          column: line.indexOf('JSON.parse'),
          message: 'JSON.parse should be wrapped in try-catch for error handling',
          rule: 'safe-json-parse',
          fixable: true,
          suggestedFix: 'Wrap in try-catch block'
        });
      }

      // Detect long lines that hurt readability
      if (line.length > 120) {
        issues.push({
          type: 'maintainability',
          severity: 'low',
          line: lineNumber,
          column: 120,
          message: 'Line too long, consider breaking into multiple lines',
          rule: 'max-line-length',
          fixable: true,
          suggestedFix: 'Break line at logical points'
        });
      }

      // Detect potential memory leaks
      if (line.includes('setInterval') && !line.includes('clearInterval')) {
        issues.push({
          type: 'performance',
          severity: 'high',
          line: lineNumber,
          column: line.indexOf('setInterval'),
          message: 'setInterval without clearInterval can cause memory leaks',
          rule: 'no-memory-leak',
          fixable: true,
          suggestedFix: 'Add corresponding clearInterval in cleanup'
        });
      }
    });

    return issues;
  }

  // Generate optimization suggestions
  private async generateOptimizationSuggestions(
    sourceCode: string, 
    metrics: CodeMetrics
  ): Promise<OptimizationSuggestion[]> {
    const suggestions: OptimizationSuggestion[] = [];

    // Suggest memoization for expensive operations
    if (sourceCode.includes('JSON.parse') || sourceCode.includes('JSON.stringify')) {
      suggestions.push({
        id: 'memoize-json',
        type: 'performance',
        priority: 'medium',
        description: 'Add memoization for JSON operations to improve performance',
        currentCode: 'JSON.parse(data)',
        optimizedCode: 'memoizedJsonParse(data)',
        expectedBenefit: 'Reduce JSON parsing overhead by 30-50%',
        estimatedEffort: 2,
        riskLevel: 'low'
      });
    }

    // Suggest code splitting for large functions
    if (metrics.linesOfCode > 200) {
      suggestions.push({
        id: 'split-large-functions',
        type: 'readability',
        priority: 'high',
        description: 'Break down large functions into smaller, more manageable pieces',
        currentCode: '// Large function with 200+ lines',
        optimizedCode: '// Multiple smaller functions with clear responsibilities',
        expectedBenefit: 'Improve maintainability and testability',
        estimatedEffort: 4,
        riskLevel: 'medium'
      });
    }

    // Suggest reducing cyclomatic complexity
    if (metrics.cyclomaticComplexity > 10) {
      suggestions.push({
        id: 'reduce-complexity',
        type: 'architecture',
        priority: 'high',
        description: 'Reduce cyclomatic complexity by extracting conditional logic',
        currentCode: '// Complex nested if-else statements',
        optimizedCode: '// Strategy pattern or lookup tables',
        expectedBenefit: 'Improve code readability and reduce bugs',
        estimatedEffort: 6,
        riskLevel: 'medium'
      });
    }

    // Suggest performance optimizations
    if (sourceCode.includes('forEach') && sourceCode.includes('push')) {
      suggestions.push({
        id: 'optimize-array-operations',
        type: 'performance',
        priority: 'medium',
        description: 'Replace forEach with map/filter for better performance',
        currentCode: 'array.forEach(item => result.push(transform(item)))',
        optimizedCode: 'const result = array.map(transform)',
        expectedBenefit: 'Improve performance and functional programming style',
        estimatedEffort: 1,
        riskLevel: 'low'
      });
    }

    return suggestions;
  }

  // Detect duplicated code patterns
  private detectDuplicatedCode(sourceCode: string): number {
    const lines = sourceCode.split('\n').map(line => line.trim()).filter(line => line);
    const lineMap = new Map<string, number>();
    let duplicatedCount = 0;

    lines.forEach(line => {
      if (line.length > 10) { // Only check meaningful lines
        const count = lineMap.get(line) || 0;
        lineMap.set(line, count + 1);
        if (count === 1) { // First duplicate
          duplicatedCount += 2; // Original + duplicate
        } else if (count > 1) { // Additional duplicates
          duplicatedCount++;
        }
      }
    });

    return duplicatedCount;
  }

  // Utility methods
  private hashCode(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private isCacheValid(result: CodeAnalysisResult): boolean {
    // For now, always consider cache valid within expiry time
    return true;
  }

  private getDefaultMetrics(): CodeMetrics {
    return {
      linesOfCode: 0,
      cyclomaticComplexity: 1,
      maintainabilityIndex: 100,
      duplicatedLines: 0,
      testCoverage: 0,
      technicalDebt: 0
    };
  }

  private getDefaultComplexity(): ComplexityMetrics {
    return {
      cyclomaticComplexity: 1,
      cognitiveComplexity: 0,
      nestingDepth: 0,
      functionLength: 0,
      parameterCount: 0
    };
  }

  // Get analysis summary for all cached results
  getAnalysisSummary(): { totalFiles: number; totalIssues: number; averageComplexity: number } {
    const results = Array.from(this.analysisCache.values());
    
    return {
      totalFiles: results.length,
      totalIssues: results.reduce((sum, result) => sum + result.issues.length, 0),
      averageComplexity: results.length > 0 
        ? results.reduce((sum, result) => sum + result.complexity.cyclomaticComplexity, 0) / results.length
        : 0
    };
  }

  // Clear analysis cache
  clearCache(): void {
    this.analysisCache.clear();
  }
}
