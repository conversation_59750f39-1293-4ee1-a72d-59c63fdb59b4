// Conversation Context Manager for Chitu00
// Manages conversation flow, context, and topic tracking

import { v4 as uuidv4 } from 'uuid';

export interface ConversationTurn {
  id: string;
  timestamp: Date;
  userMessage: string;
  aiResponse: string;
  language: string;
  intent: string;
  sentiment: string;
  topics: string[];
  entities: any[];
  confidence: number;
}

export interface ConversationContext {
  sessionId: string;
  userId?: string;
  startTime: Date;
  lastActivity: Date;
  turns: ConversationTurn[];
  currentTopics: TopicContext[];
  activeEntities: EntityContext[];
  conversationState: ConversationState;
  metadata: ConversationMetadata;
}

export interface TopicContext {
  topic: string;
  relevance: number;
  firstMentioned: Date;
  lastMentioned: Date;
  frequency: number;
  relatedTopics: string[];
}

export interface EntityContext {
  entity: string;
  type: string;
  value: string;
  firstMentioned: Date;
  lastMentioned: Date;
  frequency: number;
  confidence: number;
}

export interface ConversationState {
  phase: 'greeting' | 'exploration' | 'deep_conversation' | 'closing' | 'idle';
  mood: string;
  energy: number;
  engagement: number;
  coherence: number;
  lastIntent: string;
  expectingResponse: boolean;
}

export interface ConversationMetadata {
  totalTurns: number;
  averageResponseTime: number;
  languageDistribution: { [language: string]: number };
  topicDiversity: number;
  sentimentTrend: string[];
  userEngagement: number;
}

export interface ContextualResponse {
  response: string;
  confidence: number;
  contextRelevance: number;
  topicContinuity: number;
  personalityAlignment: number;
  suggestedFollowUps: string[];
}

export class ConversationContextManager {
  private contexts: Map<string, ConversationContext>;
  private maxContextAge: number = 24 * 60 * 60 * 1000; // 24 hours
  private maxTurnsPerContext: number = 1000;

  constructor() {
    this.contexts = new Map();
    this.startCleanupTimer();
  }

  // Create new conversation context
  createContext(userId?: string): string {
    const sessionId = uuidv4();
    const context: ConversationContext = {
      sessionId,
      userId,
      startTime: new Date(),
      lastActivity: new Date(),
      turns: [],
      currentTopics: [],
      activeEntities: [],
      conversationState: {
        phase: 'greeting',
        mood: 'neutral',
        energy: 0.7,
        engagement: 0.5,
        coherence: 1.0,
        lastIntent: '',
        expectingResponse: false
      },
      metadata: {
        totalTurns: 0,
        averageResponseTime: 0,
        languageDistribution: {},
        topicDiversity: 0,
        sentimentTrend: [],
        userEngagement: 0.5
      }
    };

    this.contexts.set(sessionId, context);
    return sessionId;
  }

  // Add conversation turn
  addTurn(sessionId: string, turn: Omit<ConversationTurn, 'id'>): void {
    const context = this.contexts.get(sessionId);
    if (!context) return;

    const fullTurn: ConversationTurn = {
      id: uuidv4(),
      ...turn
    };

    context.turns.push(fullTurn);
    context.lastActivity = new Date();
    context.metadata.totalTurns++;

    // Update language distribution
    context.metadata.languageDistribution[turn.language] = 
      (context.metadata.languageDistribution[turn.language] || 0) + 1;

    // Update sentiment trend
    context.metadata.sentimentTrend.push(turn.sentiment);
    if (context.metadata.sentimentTrend.length > 10) {
      context.metadata.sentimentTrend.shift();
    }

    // Update topics and entities
    this.updateTopics(context, fullTurn.topics);
    this.updateEntities(context, fullTurn.entities);
    this.updateConversationState(context, fullTurn);

    // Cleanup old turns if necessary
    if (context.turns.length > this.maxTurnsPerContext) {
      context.turns = context.turns.slice(-this.maxTurnsPerContext);
    }
  }

  // Get conversation context
  getContext(sessionId: string): ConversationContext | undefined {
    return this.contexts.get(sessionId);
  }

  // Get contextual response suggestions
  getContextualResponse(sessionId: string, userMessage: string, intent: string): ContextualResponse {
    const context = this.contexts.get(sessionId);
    if (!context) {
      return this.getDefaultResponse();
    }

    const recentTurns = context.turns.slice(-5);
    const currentTopics = context.currentTopics.slice(0, 3);
    
    // Calculate context relevance
    const contextRelevance = this.calculateContextRelevance(userMessage, recentTurns);
    
    // Calculate topic continuity
    const topicContinuity = this.calculateTopicContinuity(userMessage, currentTopics);
    
    // Generate response based on context
    const response = this.generateContextualResponse(context, userMessage, intent);
    
    // Calculate confidence
    const confidence = (contextRelevance + topicContinuity + context.conversationState.coherence) / 3;
    
    // Generate follow-up suggestions
    const suggestedFollowUps = this.generateFollowUpSuggestions(context, intent);

    return {
      response,
      confidence,
      contextRelevance,
      topicContinuity,
      personalityAlignment: 0.8, // This would come from personality engine
      suggestedFollowUps
    };
  }

  // Get conversation summary
  getConversationSummary(sessionId: string): string {
    const context = this.contexts.get(sessionId);
    if (!context) return '';

    const topTopics = context.currentTopics
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, 3)
      .map(t => t.topic);

    const dominantLanguage = Object.entries(context.metadata.languageDistribution)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'en';

    const avgSentiment = this.calculateAverageSentiment(context.metadata.sentimentTrend);

    return `Conversation with ${context.metadata.totalTurns} turns in ${dominantLanguage}. ` +
           `Main topics: ${topTopics.join(', ')}. ` +
           `Overall sentiment: ${avgSentiment}. ` +
           `Current phase: ${context.conversationState.phase}.`;
  }

  private updateTopics(context: ConversationContext, topics: string[]): void {
    const now = new Date();
    
    for (const topic of topics) {
      const existingTopic = context.currentTopics.find(t => t.topic === topic);
      
      if (existingTopic) {
        existingTopic.frequency++;
        existingTopic.lastMentioned = now;
        existingTopic.relevance = Math.min(1, existingTopic.relevance + 0.1);
      } else {
        context.currentTopics.push({
          topic,
          relevance: 0.7,
          firstMentioned: now,
          lastMentioned: now,
          frequency: 1,
          relatedTopics: []
        });
      }
    }

    // Decay relevance of topics not mentioned recently
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    context.currentTopics.forEach(topic => {
      if (topic.lastMentioned < oneHourAgo) {
        topic.relevance = Math.max(0, topic.relevance - 0.05);
      }
    });

    // Remove topics with very low relevance
    context.currentTopics = context.currentTopics.filter(t => t.relevance > 0.1);
    
    // Sort by relevance and keep top 10
    context.currentTopics.sort((a, b) => b.relevance - a.relevance);
    context.currentTopics = context.currentTopics.slice(0, 10);
  }

  private updateEntities(context: ConversationContext, entities: any[]): void {
    const now = new Date();
    
    for (const entity of entities) {
      const existingEntity = context.activeEntities.find(e => 
        e.entity === entity.value && e.type === entity.type
      );
      
      if (existingEntity) {
        existingEntity.frequency++;
        existingEntity.lastMentioned = now;
        existingEntity.confidence = Math.max(existingEntity.confidence, entity.confidence);
      } else {
        context.activeEntities.push({
          entity: entity.value,
          type: entity.type,
          value: entity.value,
          firstMentioned: now,
          lastMentioned: now,
          frequency: 1,
          confidence: entity.confidence
        });
      }
    }

    // Remove old entities
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    context.activeEntities = context.activeEntities.filter(e => 
      e.lastMentioned > oneHourAgo && e.confidence > 0.3
    );
  }

  private updateConversationState(context: ConversationContext, turn: ConversationTurn): void {
    const state = context.conversationState;
    
    // Update phase based on conversation progress
    if (context.metadata.totalTurns <= 2) {
      state.phase = 'greeting';
    } else if (context.metadata.totalTurns <= 10) {
      state.phase = 'exploration';
    } else {
      state.phase = 'deep_conversation';
    }

    // Update mood based on recent sentiment
    const recentSentiments = context.metadata.sentimentTrend.slice(-3);
    const positiveCount = recentSentiments.filter(s => s === 'positive').length;
    const negativeCount = recentSentiments.filter(s => s === 'negative').length;
    
    if (positiveCount > negativeCount) {
      state.mood = 'positive';
    } else if (negativeCount > positiveCount) {
      state.mood = 'negative';
    } else {
      state.mood = 'neutral';
    }

    // Update engagement based on response length and frequency
    const avgResponseLength = context.turns
      .slice(-5)
      .reduce((sum, t) => sum + t.userMessage.length, 0) / Math.min(5, context.turns.length);
    
    state.engagement = Math.min(1, avgResponseLength / 100);

    // Update energy based on conversation activity
    const timeSinceLastTurn = Date.now() - context.lastActivity.getTime();
    if (timeSinceLastTurn < 30000) { // Less than 30 seconds
      state.energy = Math.min(1, state.energy + 0.1);
    } else {
      state.energy = Math.max(0.3, state.energy - 0.05);
    }

    state.lastIntent = turn.intent;
  }

  private calculateContextRelevance(message: string, recentTurns: ConversationTurn[]): number {
    if (recentTurns.length === 0) return 0.5;
    
    const recentTopics = recentTurns.flatMap(t => t.topics);
    const messageWords = message.toLowerCase().split(/\s+/);
    
    const relevantWords = messageWords.filter(word => 
      recentTopics.some(topic => topic.toLowerCase().includes(word))
    );
    
    return Math.min(1, relevantWords.length / messageWords.length * 2);
  }

  private calculateTopicContinuity(message: string, currentTopics: TopicContext[]): number {
    if (currentTopics.length === 0) return 0.5;
    
    const messageWords = message.toLowerCase().split(/\s+/);
    const topicWords = currentTopics.flatMap(t => t.topic.toLowerCase().split(/\s+/));
    
    const continuityScore = messageWords.filter(word => 
      topicWords.includes(word)
    ).length / messageWords.length;
    
    return Math.min(1, continuityScore * 3);
  }

  private generateContextualResponse(context: ConversationContext, message: string, intent: string): string {
    const state = context.conversationState;
    const topTopics = context.currentTopics.slice(0, 2).map(t => t.topic);
    
    // This is a simplified response generation - in practice, this would integrate
    // with the multilingual response generator
    let response = '';
    
    switch (intent) {
      case 'greeting':
        response = state.phase === 'greeting' ? 
          'Hello! Nice to meet you!' : 
          'Hello again! How can I help you today?';
        break;
      case 'question':
        response = topTopics.length > 0 ? 
          `That's an interesting question about ${topTopics[0]}. Let me think about that...` :
          'That\'s a great question! Let me help you with that.';
        break;
      default:
        response = 'I understand. Let me help you with that.';
    }
    
    return response;
  }

  private generateFollowUpSuggestions(context: ConversationContext, intent: string): string[] {
    const suggestions: string[] = [];
    const topTopics = context.currentTopics.slice(0, 2);
    
    switch (intent) {
      case 'question':
        suggestions.push('Would you like me to explain that in more detail?');
        if (topTopics.length > 0) {
          suggestions.push(`What else would you like to know about ${topTopics[0].topic}?`);
        }
        break;
      case 'compliment':
        suggestions.push('Thank you! Is there anything specific I can help you with?');
        break;
      case 'greeting':
        suggestions.push('What would you like to talk about today?');
        suggestions.push('How are you feeling today?');
        break;
    }
    
    return suggestions;
  }

  private calculateAverageSentiment(sentiments: string[]): string {
    if (sentiments.length === 0) return 'neutral';
    
    const counts = sentiments.reduce((acc, sentiment) => {
      acc[sentiment] = (acc[sentiment] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });
    
    const dominant = Object.entries(counts)
      .sort(([,a], [,b]) => b - a)[0]?.[0];
    
    return dominant || 'neutral';
  }

  private getDefaultResponse(): ContextualResponse {
    return {
      response: 'I understand. How can I help you?',
      confidence: 0.5,
      contextRelevance: 0.5,
      topicContinuity: 0.5,
      personalityAlignment: 0.5,
      suggestedFollowUps: ['What would you like to know?', 'How can I assist you?']
    };
  }

  private startCleanupTimer(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [sessionId, context] of this.contexts.entries()) {
        if (now - context.lastActivity.getTime() > this.maxContextAge) {
          this.contexts.delete(sessionId);
        }
      }
    }, 60 * 60 * 1000); // Run every hour
  }

  // Get active sessions count
  getActiveSessionsCount(): number {
    return this.contexts.size;
  }

  // Clear old contexts
  clearOldContexts(): void {
    const now = Date.now();
    for (const [sessionId, context] of this.contexts.entries()) {
      if (now - context.lastActivity.getTime() > this.maxContextAge) {
        this.contexts.delete(sessionId);
      }
    }
  }
}
